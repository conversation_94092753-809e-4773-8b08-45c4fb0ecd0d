import { useState, useContext } from 'react';
import { BasePlans } from './base-plans/base-plans';
import { EnterprisePlans } from './interprise-plans/enterprise-plans';
import { AppContext } from '/src/components/provider';

import { Button } from 'src';
export const AllPlans = ({ length: initialLength }) => {
  const { userData } = useContext(AppContext);
  const isPermittedAdmin = userData?.roles.some((role) => ['admin'].includes(role));
  const [activeTab, setActiveTab] = useState('base');
  const [plansLength, setPlansLength] = useState(initialLength || 0);
  const [enterprisePlansLength, setEnterprisePlansLength] = useState(initialLength || 0);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleBasePlansDataLoaded = (length) => {
    setPlansLength(length);
  };

  return (
    <div className="w-full  my-6">
      <div className="flex justify-between flex-wrap items-center mb-4">
        {/* Tab Navigation */}

        {!isPermittedAdmin && enterprisePlansLength == 0 && (
          <div className={`flex text-nowrap flex-nowrap   rounded-lg border  overflow-hidden w-fit mb-4 `}>
            <button
              className={`flex items-center   p-5 text-sm font-medium ${
                activeTab === 'base' ? 'bg-[#F7F3FF] text-purple-700' : 'bg-transparent text-gray-700 hover:text-gray-800'
              }`}
              onClick={() => handleTabChange('base')}
            >
              Base Plans {'  '}
              <span className="bg-[#f9f5ff] dark:bg-[#1f1a2e] text-primaryPurple text-sm font-medium rounded-full px-2.5 ml-2 border">
                {plansLength}
              </span>
            </button>

            <button
              className={`flex items-center p-5 text-sm font-medium ${
                activeTab === 'enterprise' ? 'bg-purple-50 text-purple-700' : 'bg-transparent text-gray-700 hover:text-gray-800'
              }`}
              onClick={() => handleTabChange('enterprise')}
            >
              Enterprise Plans
              <span
                className={`bg-[#f9f5ff] dark:bg-[#1f1a2e] text-primaryPurple text-sm font-medium rounded-full px-2.5 ml-2 border ${
                  activeTab === 'enterprise' ? 'text-purple-700' : 'text-gray-500'
                }`}
              >
                {enterprisePlansLength}
              </span>
            </button>
          </div>
        )}

        {/* <div className={`flex text-nowrap flex-nowrap   rounded-lg border  overflow-hidden w-fit mb-4 `}>
          <button
            className={`flex items-center   p-5 text-sm font-medium ${
              activeTab === 'base' ? 'bg-[#F7F3FF] text-purple-700' : 'bg-transparent text-gray-700 hover:text-gray-800'
            }`}
            onClick={() => handleTabChange('base')}
          >
            Monthly
          </button>

          <button
            className={`flex items-center p-5 text-sm font-medium ${
              activeTab === 'enterprise' ? 'bg-purple-50 text-purple-700' : 'bg-transparent text-gray-700 hover:text-gray-800'
            }`}
            onClick={() => handleTabChange('enterprise')}
          >
            Yearly
          </button>
        </div> */}
      </div>
      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'base' && <BasePlans onDataLoaded={handleBasePlansDataLoaded} />}
        {activeTab === 'enterprise' && <EnterprisePlans />}
      </div>
    </div>
  );
};
