import React, { useEffect } from 'react';

import { Dialog, Form, TextInput, RadioGroup, Button, useForm, useNotify, Api, Regex, useValidate, Select, PhoneNumberInput } from '/src';

export const ApplicantsSingleDialog = ({ onClose, onCreate, id }) => {
  // Hooks
  const { notify } = useNotify();
  const { isRequired, validateRegex, minLength, maxLength, countryCodeNumberValid } = useValidate();

  // Form
  const { form, setFieldValue, setFormValue, resetForm } = useForm({
    name: '',
    gender: 1,
    email: '',
    track: '',
    seniorityLevel: 0,
    mobileNumber: '',
    notes: '',
    // address: '',
  });

  const handleGet = async () => {
    try {
      const response = await Api.get(`applicants/single/${id}`);

      setFormValue(response.data);
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleInsert = async (e) => {
    try {
      let payload = { ...form };
      if (payload.track === '') {
        delete payload.track;
      }
      await Api.post('applicants/single', payload);

      onCreate();
      notify('Applicant added successfully!');
      onClose();
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  const handleUpdate = async (e) => {
    try {
      await Api.put(`applicants/single/${id}`, form);

      onCreate();
      notify('Applicant updated successfully!');
      onClose();
    } catch (error) {
      notify.error(error.response.data.message);
    }
  };

  // Getters
  const isEditMode = () => !!form._id;

  // On mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, []);

  return (
    <Dialog size="lg" show popup modalHeader={id ? 'Edit Applicant' : 'Create Applicant'} onClose={onClose} overflowVisible={true}>
      {/* Creation Form */}

      <Form onSubmit={isEditMode() ? handleUpdate : handleInsert}>
        <div className="grid gap-4">
          <div className="flex flex-col gap-2">
            <TextInput
              label="Name"
              name="name"
              placeholder="Enter applicant’s name"
              value={form.name}
              onChange={setFieldValue('name')}
              validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50)]}
              requiredLabel
            />

            <div className="mb-1">
              <RadioGroup
                name="gender"
                label="Gender"
                value={form.gender}
                onChange={setFieldValue('gender', Number)}
                lookup="$Gender"
                className="text-inputLabel dark:text-inputDarkLabel"
                requiredLabel
              />
            </div>

            <TextInput
              label="Email address"
              name="email"
              placeholder="Enter applicant’s email address"
              value={form.email}
              onChange={setFieldValue('email')}
              validators={[isRequired(), validateRegex(Regex.email)]}
              requiredLabel
            />

            {/* <TextInput
              label="Address"
              name="address"
              placeholder="Applicant address"
              value={form.address}
              onChange={setFieldValue('address')}
              validators={[isRequired()]}
            /> */}

            <PhoneNumberInput
              label="Mobile Number"
              value={form.mobileNumber}
              onChange={setFieldValue('mobileNumber')}
              requiredLabel
              validators={[countryCodeNumberValid()]}
            />

            {/* <div className="space-y-3">
              <Select
                label="Track"
                name="track"
                value={form.track}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon={true}
                onChange={(selectedTrack) => {
                  setFieldValue('track')(selectedTrack);
                }}
                validators={[isRequired()]}
                creationOptions={{
                  url: 'lookups/category/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
                requiredLabel
                placeholder="Select applicant’s track"
              />
            </div> */}

            <Select
              label="Level"
              name="SeniorityLevel"
              value={form.seniorityLevel}
              onChange={setFieldValue('seniorityLevel', Number)}
              lookup="$QuizDifficulty"
              dropIcon={true}
              validators={[isRequired()]}
              placeholder="Select applicant’s level"
              requiredLabel
            />
          </div>
        </div>

        <Button className="w-full mt-3" type="submit" label={`${!!id ? 'Update' : 'Create'}`} />
      </Form>
    </Dialog>
  );
};
