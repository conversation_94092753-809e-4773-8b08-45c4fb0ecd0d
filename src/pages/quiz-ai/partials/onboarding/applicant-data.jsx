// React
import { useEffect, useContext } from 'react';

// Context
import { SubmissionAiContext } from '/src/pages/quiz-ai';

// Components
import { Form, TextInput, useForm, useValidate, RadioGroup, Button, Api, Regex, PhoneNumberInput } from '/src';
import { api } from '../../../../services/axios';

import { motion } from 'framer-motion';

export const ApplicantData = ({ handleStart, setShowApplicantForm }) => {
  // Hooks
  const { isRequired, minLength, maxLength, validateRegex, countryCodeNumberValid } = useValidate();

  // Context
  const { notify, loading, setLoading, submissionAi, setSubmissionAi, handleGetSubmissionAi } = useContext(SubmissionAiContext);

  const { form, setFieldValue, setFormValue } = useForm({
    name: '',
    email: submissionAi?.applicant?.email,
    mobileNumber: '',
    gender: 1,
    _id: submissionAi?.applicant?._id,
    seniorityLevel: submissionAi.applicant?.seniorityLevel || submissionAi.interview?.difficulty,
    track: submissionAi.interview?.category?.categoryId,
  });

  const onSubmit = async () => {
    if (loading) return; // Prevent multiple submissions

    try {
      setLoading(true);

      if (submissionAi.applicant) {
        const { name, email, mobileNumber, gender } = submissionAi.applicant;
        if (name && email && mobileNumber && gender) {
          await handleStart(submissionAi.interview._id);
        } else {
          const response = await updateApplicantData({ ...submissionAi, applicant: form });
          if (response?.success) {
            setSubmissionAi({ ...submissionAi, applicant: form });
            await handleStart(submissionAi.interview._id);
          }
        }
      } else {
        // Create new applicant
        const payload = { ...form, randomId: submissionAi?.interview?.randomId };
        const applicantResponse = await Api.post(`applicants/single/custom/create`, payload);
        // Create new interview with (new applicant, new interview)
        const interviewResponse = await Api.post('ai-interview/create/custom', {
          randomId: submissionAi?.interview?.randomId,
          applicantId: applicantResponse?.data?._id,
        });
        setSubmissionAi({ interview: interviewResponse.data, applicant: applicantResponse.data });
        await handleStart(interviewResponse.data._id);
      }
    } catch (error) {
      notify.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const updateApplicantData = async (data) => {
    try {
      const response = await api.put(`applicants/single/custom/${submissionAi?.applicant?._id}`, data.applicant);
      return response.data;
    } catch (error) {
      notify.error(error.respsetFormValueonse?.data.message);
    }
  };

  useEffect(() => {
    if (submissionAi.applicant) {
      setFormValue(submissionAi.applicant);
    }
  }, []);

  return (
    <div className="w-full h-full">
      <motion.div initial={{ opacity: 0, x: 100 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -100 }} transition={{ duration: 0.2 }}>
        <Form id="applicantForm" onSubmit={onSubmit}>
          <div className="mt-12 w-full border dark:border-[#4c4e56d4] dark:border-gray-700 p-6 rounded-xl overflow-hidden max-w-[800px]">
            <div className="mb-5">
              {' '}
              <div>
                <p className="text-[22px] font-medium mb-2 text-darkGrayBackground dark:text-white ">Applicant Details</p>
              </div>
              {!submissionAi.applicant && (
                <p className="text-base text-gray-500 dark:text-gray-400">Please fill in the following details to proceed with your application.</p>
              )}
            </div>

            <div className="flex flex-col  gap-6 w-full dark:[&_input]:bg-gray-700 dark:[&_input]:border-transparent [&_input]:border-gray-300 dark:[&_input]:text-white [&_input]:text-black [&_input]:placeholder:text-[#707070] [&_label]:text-black dark:[&_label]:text-white [&_label]:mb-2">
              <TextInput
                className="dark:text-white  "
                label="Name"
                name="name"
                placeholder="Name"
                value={form.name}
                onChange={setFieldValue('name')}
                validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50)]}
                disabled={submissionAi?.applicant?.name}
              />
              <div className="dark:[&_*]:text-white [&_*]:text-inputLabel [&_label]:mb-0  ">
                <RadioGroup name="gender" label="Gender" value={form.gender} onChange={setFieldValue('gender', Number)} lookup="$Gender" />
              </div>
              <TextInput
                label="Email"
                name="email"
                placeholder="Email"
                value={form.email}
                onChange={setFieldValue('email')}
                validators={[isRequired(), validateRegex(Regex.email)]}
                disabled={submissionAi?.applicant?.email}
              />
              {/* Mobile Number Input */}
              <PhoneNumberInput
                label="Mobile Number"
                value={form.mobileNumber}
                onChange={setFieldValue('mobileNumber')}
                validators={[countryCodeNumberValid()]}
                disabled={submissionAi?.applicant?.mobileNumber}
              />
            </div>
          </div>
          <div className="flex justify-center gap-5 mt-10">
            <Button
              tertiary
              form="applicantForm"
              className="p-1 mt-1"
              label="Back: Instructions"
              icon="fluent:arrow-left-20-filled"
              iconWidth="20"
              onClick={() => setShowApplicantForm(false)}
              disabled={loading}
            />
            <Button
              tertiary
              type="submit"
              form="applicantForm"
              className="p-1 mt-1"
              label={loading ? 'Preparing interview...' : 'Start your Interview'}
              iconRight={loading ? null : 'fluent:arrow-right-20-filled'}
              iconWidth="20"
              loading={loading}
              disabled={loading}
              disabledMessage="Please wait while we prepare your interview"
            />
          </div>
        </Form>
      </motion.div>
    </div>
  );
};
